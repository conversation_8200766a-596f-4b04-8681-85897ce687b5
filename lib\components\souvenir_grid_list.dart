import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/souvenir/souvenir_item.dart';
import '../models/souvenir/category_enum.dart';
import '../providers/souvenir_provider.dart';
import 'category_tabs.dart';

/// 奖励网格列表组件
///
/// 展示三列网格布局的奖励列表
/// 支持下拉刷新和上拉加载更多
class SouvenirGridList extends ConsumerStatefulWidget {
  const SouvenirGridList({super.key});

  @override
  ConsumerState<SouvenirGridList> createState() => _SouvenirGridListState();
}

class _SouvenirGridListState extends ConsumerState<SouvenirGridList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 监听滚动事件，实现上拉加载
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听方法
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 400) {
      // 距离底部400px时开始加载更多
      final selectedCategory = ref.read(selectedCategoryProvider);
      final query = SouvenirQuery(
        category: selectedCategory == ItemCategory.all ? null : selectedCategory.value,
      );
      ref.read(souvenirListProvider(query).notifier).loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedCategory = ref.watch(selectedCategoryProvider);
    final query = SouvenirQuery(
      category: selectedCategory == ItemCategory.all ? null : selectedCategory.value,
    );
    final souvenirState = ref.watch(souvenirListProvider(query));

    // 监听分类变化并重新加载数据
    ref.listen(selectedCategoryProvider, (prev, next) {
      if (prev != next) {
        final newQuery = SouvenirQuery(
          category: next == ItemCategory.all ? null : next.value,
        );
        ref.read(souvenirListProvider(newQuery).notifier).refresh();
      }
    });

    // 错误状态
    if (souvenirState.error != null && souvenirState.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              souvenirState.error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(souvenirListProvider(query).notifier).refresh();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // 初始加载状态
    if (souvenirState.isLoading && souvenirState.items.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    // 空数据状态
    if (souvenirState.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.card_giftcard_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text('暂无奖励数据', style: TextStyle(color: Colors.grey[600])),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(souvenirListProvider(query).notifier).refresh();
      },
      child: GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // 三列布局
          crossAxisSpacing: 16, // 列间距
          mainAxisSpacing: 20, // 行间距
          childAspectRatio: 0.75, // 宽高比，调整卡片形状
        ),
        itemCount: souvenirState.items.length + (souvenirState.isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          // 加载更多指示器
          if (index == souvenirState.items.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final item = souvenirState.items[index];
          return SouvenirCard(item: item);
        },
      ),
    );
  }
}

/// 单个奖励卡片组件
class SouvenirCard extends StatelessWidget {
  final SouvenirItem item;

  const SouvenirCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片区域
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
              border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04),
                  offset: const Offset(0, 1),
                  blurRadius: 2,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(11), // 稍微小一点以适应边框
              child: CachedNetworkImage(
                imageUrl: item.imgUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: const Color(0xFFF8F8F8),
                  child: Center(
                    child: Icon(
                      Icons.card_giftcard_outlined,
                      size: 32,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: const Color(0xFFF8F8F8),
                  child: Center(
                    child: Icon(
                      Icons.broken_image_outlined,
                      size: 32,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 10),
        // 标题区域
        Text(
          item.title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
            height: 1.3,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
