import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../components/category_tabs.dart';
import '../components/souvenir_grid_list.dart';

/// 奖励列表页面
///
/// 整合分类筛选和奖励列表组件
class SouvenirListPage extends ConsumerWidget {
  const SouvenirListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: const Column(
          children: [
            // 分类筛选标签
            CategoryTabs(),
            // 三列网格列表
            Expanded(child: SouvenirGridList()),
          ],
        ),
      ),
    );
  }
}
